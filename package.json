{"name": "@sainpse/portal-next", "private": true, "version": "1.0.0", "type": "module", "engines": {"node": ">=20.0.0", "bun": ">=1.0.0"}, "scripts": {"dev": "vite --host 0.0.0.0 --port 8080", "dev:turbo": "vite --mode turbo", "build": "tsc -b && vite build", "build:analyze": "vite build --mode analyze", "build:dev": "vite build --mode development", "preview": "vite preview --host 0.0.0.0", "lint": "biome check .", "lint:fix": "biome check --write .", "format": "biome format --write .", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "perf": "lighthouse http://localhost:8080 --output=html --output-path=./reports/lighthouse.html", "bundle-analyzer": "npx vite-bundle-analyzer"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^5.56.2", "@tanstack/react-virtual": "^3.10.8", "@vanilla-extract/css": "^1.16.0", "@vanilla-extract/vite-plugin": "^4.0.15", "binance-fiat-widget": "^1.1.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "fp-ts": "^2.16.9", "framer-motion": "^12.4.11", "immer": "^10.1.1", "input-otp": "^1.2.4", "jotai": "^2.10.0", "lucide-react": "^0.462.0", "motion": "^12.4.3", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-error-boundary": "^4.0.13", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.13.1", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.29.0", "react-spinners": "^0.15.0", "react-spring": "^9.7.4", "react-use": "^17.6.0", "react-use-gesture": "^9.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "surrealdb.js": "^1.0.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "three": "^0.169.0", "use-debounce": "^10.0.3", "vaul": "^0.9.3", "web-vitals": "^4.2.3", "workbox-window": "^7.1.0", "zod": "^3.23.8", "zustand": "^5.0.0"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/three": "^0.169.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^2.1.4", "@vitest/ui": "^2.1.4", "autoprefixer": "^10.4.20", "happy-dom": "^15.7.4", "lighthouse": "^12.2.1", "lovable-tagger": "^1.0.19", "postcss": "^8.4.47", "rollup-plugin-visualizer": "^5.12.0", "tailwindcss": "^3.4.11", "typescript": "^5.6.3", "vite": "^5.4.14", "vite-bundle-analyzer": "^0.11.0", "vite-plugin-pwa": "^0.20.5", "vitest": "^2.1.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "packageManager": "bun@1.1.38"}