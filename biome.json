{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["node_modules/**", "dist/**", "build/**", "coverage/**", "*.min.js", "*.bundle.js"]}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 100, "attributePosition": "auto", "ignore": ["**/*.md", "**/*.json"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"recommended": true, "useKeyWithClickEvents": "error", "useKeyWithMouseEvents": "error"}, "complexity": {"recommended": true, "noExtraBooleanCast": "error", "noMultipleSpacesInRegularExpressionLiterals": "error", "noUselessCatch": "error", "noUselessTypeConstraint": "error"}, "correctness": {"recommended": true, "noUnusedVariables": "warn", "useExhaustiveDependencies": "warn", "useHookAtTopLevel": "error"}, "performance": {"recommended": true, "noAccumulatingSpread": "warn", "noDelete": "error"}, "security": {"recommended": true, "noDangerouslySetInnerHtml": "warn"}, "style": {"recommended": true, "noNegationElse": "off", "useConst": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useExponentiationOperator": "error", "useFragmentSyntax": "error", "useNodejsImportProtocol": "error", "useNumberNamespace": "error", "useNumericLiterals": "error", "useSelfClosingElements": "error", "useShorthandArrayType": "error", "useShorthandAssign": "error", "useSingleVarDeclarator": "error", "useTemplate": "error"}, "suspicious": {"recommended": true, "noArrayIndexKey": "warn", "noAssignInExpressions": "error", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCommentText": "error", "noCompareNegZero": "error", "noConsoleLog": "warn", "noControlCharactersInRegex": "error", "noDebugger": "warn", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "warn", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalObjectCalls": "error", "noImportAssign": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "useGetterReturn": "error", "useValidTypeof": "error"}}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}}, "json": {"formatter": {"trailingCommas": "none"}}}