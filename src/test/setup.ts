import '@testing-library/jest-dom';
import { expect, afterEach, beforeAll, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import { server } from './mocks/server';

// Extend Vitest's expect with jest-dom matchers
expect.extend({});

// Global test setup
beforeAll(() => {
  // Start MSW server for API mocking
  server.listen({ onUnhandledRequest: 'error' });
  
  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
  
  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
  
  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
  
  // Mock crypto.randomUUID
  Object.defineProperty(global, 'crypto', {
    value: {
      randomUUID: vi.fn(() => 'test-uuid-' + Math.random().toString(36).substr(2, 9)),
    },
  });
  
  // Mock performance API
  Object.defineProperty(global, 'performance', {
    value: {
      now: vi.fn(() => Date.now()),
      mark: vi.fn(),
      measure: vi.fn(),
      getEntriesByType: vi.fn(() => []),
      getEntriesByName: vi.fn(() => []),
      clearMarks: vi.fn(),
      clearMeasures: vi.fn(),
    },
  });
  
  // Mock Web APIs
  Object.defineProperty(navigator, 'clipboard', {
    value: {
      writeText: vi.fn(() => Promise.resolve()),
      readText: vi.fn(() => Promise.resolve('')),
    },
  });
  
  Object.defineProperty(navigator, 'share', {
    value: vi.fn(() => Promise.resolve()),
  });
  
  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
    length: 0,
    key: vi.fn(),
  };
  
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
  });
  
  // Mock sessionStorage
  Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock,
  });
  
  // Mock console methods for cleaner test output
  vi.spyOn(console, 'warn').mockImplementation(() => {});
  vi.spyOn(console, 'error').mockImplementation(() => {});
  
  // Set up global test environment
  process.env.NODE_ENV = 'test';
});

// Clean up after each test
afterEach(() => {
  cleanup();
  server.resetHandlers();
  vi.clearAllMocks();
});

// Global teardown
afterAll(() => {
  server.close();
  vi.restoreAllMocks();
});

// Custom test utilities
export const createMockComponent = (name: string) => {
  return vi.fn().mockImplementation(({ children, ...props }) => {
    return React.createElement('div', {
      'data-testid': `mock-${name.toLowerCase()}`,
      ...props,
    }, children);
  });
};

// Mock Framer Motion for tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => React.createElement('div', props, children),
    span: ({ children, ...props }: any) => React.createElement('span', props, children),
    button: ({ children, ...props }: any) => React.createElement('button', props, children),
    img: ({ children, ...props }: any) => React.createElement('img', props, children),
    a: ({ children, ...props }: any) => React.createElement('a', props, children),
  },
  AnimatePresence: ({ children }: any) => children,
  useAnimation: () => ({
    start: vi.fn(),
    stop: vi.fn(),
    set: vi.fn(),
  }),
  useMotionValue: (initial: any) => ({
    get: () => initial,
    set: vi.fn(),
    on: vi.fn(),
  }),
  useTransform: () => 0,
  useScroll: () => ({
    scrollY: { get: () => 0 },
    scrollX: { get: () => 0 },
  }),
  useSpring: (value: any) => value,
  useVelocity: () => 0,
}));

// Mock React Router for tests
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      pathname: '/',
      search: '',
      hash: '',
      state: null,
    }),
    useParams: () => ({}),
  };
});

// Mock Zustand store for tests
vi.mock('@/stores/app-store', () => ({
  useAppStore: vi.fn(() => ({
    preferences: {
      theme: 'light',
      aiPersonality: 'creative',
      animationsEnabled: true,
      soundEnabled: true,
      focusMode: false,
      language: 'en',
      accessibility: {
        reducedMotion: false,
        highContrast: false,
        fontSize: 'medium',
      },
    },
    ui: {
      isLoading: false,
      activeModal: null,
      notifications: [],
      sidebarOpen: false,
      commandPaletteOpen: false,
    },
    performance: {
      loadTime: 0,
      renderTime: 0,
      interactionLatency: 0,
      memoryUsage: 0,
      bundleSize: 0,
    },
    analytics: {
      sessionId: 'test-session',
      events: [],
    },
    updatePreferences: vi.fn(),
    setLoading: vi.fn(),
    openModal: vi.fn(),
    closeModal: vi.fn(),
    addNotification: vi.fn(),
    removeNotification: vi.fn(),
    trackEvent: vi.fn(),
    toggleCommandPalette: vi.fn(),
  })),
  usePreferences: vi.fn(() => ({
    theme: 'light',
    aiPersonality: 'creative',
    animationsEnabled: true,
    soundEnabled: true,
    focusMode: false,
    language: 'en',
    accessibility: {
      reducedMotion: false,
      highContrast: false,
      fontSize: 'medium',
    },
  })),
  useAppActions: vi.fn(() => ({
    updatePreferences: vi.fn(),
    setLoading: vi.fn(),
    openModal: vi.fn(),
    closeModal: vi.fn(),
    addNotification: vi.fn(),
    removeNotification: vi.fn(),
    trackEvent: vi.fn(),
  })),
}));

// Global React import for JSX
import React from 'react';
