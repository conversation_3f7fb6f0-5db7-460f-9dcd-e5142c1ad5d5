import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';

// Mock API handlers
const handlers = [
  // Mock Supabase auth endpoints
  http.post('*/auth/v1/token', () => {
    return HttpResponse.json({
      access_token: 'mock-access-token',
      token_type: 'bearer',
      expires_in: 3600,
      refresh_token: 'mock-refresh-token',
      user: {
        id: 'mock-user-id',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
      },
    });
  }),

  // Mock Supabase data endpoints
  http.get('*/rest/v1/tools', () => {
    return HttpResponse.json([
      {
        id: 1,
        name: 'Test Tool',
        description: 'A test tool for testing',
        category: 'testing',
        url: 'https://example.com',
        created_at: new Date().toISOString(),
      },
    ]);
  }),

  http.post('*/rest/v1/tools', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({
      id: Math.floor(Math.random() * 1000),
      ...body,
      created_at: new Date().toISOString(),
    }, { status: 201 });
  }),

  // Mock error reporting endpoint
  http.post('/api/errors', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({
      id: 'error-' + Math.random().toString(36).substr(2, 9),
      status: 'received',
      timestamp: new Date().toISOString(),
    });
  }),

  // Mock analytics endpoint
  http.post('/api/analytics', async ({ request }) => {
    const body = await request.json();
    return HttpResponse.json({
      status: 'recorded',
      timestamp: new Date().toISOString(),
    });
  }),

  // Mock external APIs
  http.get('https://api.github.com/repos/:owner/:repo', ({ params }) => {
    return HttpResponse.json({
      id: 123456,
      name: params.repo,
      full_name: `${params.owner}/${params.repo}`,
      description: 'Mock repository description',
      stargazers_count: 42,
      forks_count: 7,
      language: 'TypeScript',
    });
  }),

  // Mock font loading
  http.get('https://fonts.googleapis.com/*', () => {
    return new HttpResponse('/* Mock font CSS */', {
      headers: {
        'Content-Type': 'text/css',
      },
    });
  }),

  http.get('https://fonts.gstatic.com/*', () => {
    return new HttpResponse(new ArrayBuffer(0), {
      headers: {
        'Content-Type': 'font/woff2',
      },
    });
  }),

  // Mock image loading
  http.get('https://images.unsplash.com/*', () => {
    return new HttpResponse(new ArrayBuffer(0), {
      headers: {
        'Content-Type': 'image/jpeg',
      },
    });
  }),

  // Catch-all handler for unhandled requests
  http.all('*', ({ request }) => {
    console.warn(`Unhandled ${request.method} request to ${request.url}`);
    return new HttpResponse(null, { status: 404 });
  }),
];

// Create and export the server
export const server = setupServer(...handlers);

// Export handlers for individual test customization
export { handlers };
