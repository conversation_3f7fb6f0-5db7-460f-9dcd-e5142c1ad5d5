import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { StateCreator } from 'zustand';

// Types for our ultra-modern store
interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  aiPersonality: 'professional' | 'creative' | 'quantum';
  animationsEnabled: boolean;
  soundEnabled: boolean;
  focusMode: boolean;
  language: string;
  accessibility: {
    reducedMotion: boolean;
    highContrast: boolean;
    fontSize: 'small' | 'medium' | 'large';
  };
}

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionLatency: number;
  memoryUsage: number;
  bundleSize: number;
}

interface UIState {
  isLoading: boolean;
  activeModal: string | null;
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    message: string;
    timestamp: number;
    persistent?: boolean;
  }>;
  sidebarOpen: boolean;
  commandPaletteOpen: boolean;
}

interface AppState {
  // User preferences with AI-powered defaults
  preferences: UserPreferences;
  
  // Performance monitoring
  performance: PerformanceMetrics;
  
  // UI state management
  ui: UIState;
  
  // Advanced analytics
  analytics: {
    sessionId: string;
    userId?: string;
    events: Array<{
      type: string;
      data: Record<string, unknown>;
      timestamp: number;
    }>;
  };
}

interface AppActions {
  // Preference management
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
  
  // Performance tracking
  updatePerformanceMetrics: (metrics: Partial<PerformanceMetrics>) => void;
  
  // UI state management
  setLoading: (loading: boolean) => void;
  openModal: (modalId: string) => void;
  closeModal: () => void;
  addNotification: (notification: Omit<AppState['ui']['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  toggleSidebar: () => void;
  toggleCommandPalette: () => void;
  
  // Analytics
  trackEvent: (type: string, data?: Record<string, unknown>) => void;
  clearAnalytics: () => void;
  
  // Advanced actions
  optimizePerformance: () => void;
  adaptToUserBehavior: () => void;
}

type AppStore = AppState & AppActions;

// Default state with intelligent defaults
const defaultState: AppState = {
  preferences: {
    theme: 'system',
    aiPersonality: 'creative',
    animationsEnabled: !window.matchMedia('(prefers-reduced-motion: reduce)').matches,
    soundEnabled: true,
    focusMode: false,
    language: navigator.language.split('-')[0] || 'en',
    accessibility: {
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      highContrast: window.matchMedia('(prefers-contrast: high)').matches,
      fontSize: 'medium',
    },
  },
  performance: {
    loadTime: 0,
    renderTime: 0,
    interactionLatency: 0,
    memoryUsage: 0,
    bundleSize: 0,
  },
  ui: {
    isLoading: false,
    activeModal: null,
    notifications: [],
    sidebarOpen: false,
    commandPaletteOpen: false,
  },
  analytics: {
    sessionId: crypto.randomUUID(),
    events: [],
  },
};

// Store implementation with advanced middleware
const createAppStore: StateCreator<
  AppStore,
  [['zustand/devtools', never], ['zustand/persist', unknown], ['zustand/immer', never], ['zustand/subscribeWithSelector', never]],
  [],
  AppStore
> = (set, get) => ({
  ...defaultState,
  
  // Preference management
  updatePreferences: (preferences) =>
    set(
      (state) => {
        Object.assign(state.preferences, preferences);
      },
      false,
      'updatePreferences'
    ),
    
  resetPreferences: () =>
    set(
      (state) => {
        state.preferences = defaultState.preferences;
      },
      false,
      'resetPreferences'
    ),
  
  // Performance tracking
  updatePerformanceMetrics: (metrics) =>
    set(
      (state) => {
        Object.assign(state.performance, metrics);
      },
      false,
      'updatePerformanceMetrics'
    ),
  
  // UI state management
  setLoading: (loading) =>
    set(
      (state) => {
        state.ui.isLoading = loading;
      },
      false,
      'setLoading'
    ),
    
  openModal: (modalId) =>
    set(
      (state) => {
        state.ui.activeModal = modalId;
      },
      false,
      'openModal'
    ),
    
  closeModal: () =>
    set(
      (state) => {
        state.ui.activeModal = null;
      },
      false,
      'closeModal'
    ),
    
  addNotification: (notification) =>
    set(
      (state) => {
        state.ui.notifications.push({
          ...notification,
          id: crypto.randomUUID(),
          timestamp: Date.now(),
        });
      },
      false,
      'addNotification'
    ),
    
  removeNotification: (id) =>
    set(
      (state) => {
        state.ui.notifications = state.ui.notifications.filter((n) => n.id !== id);
      },
      false,
      'removeNotification'
    ),
    
  clearNotifications: () =>
    set(
      (state) => {
        state.ui.notifications = [];
      },
      false,
      'clearNotifications'
    ),
    
  toggleSidebar: () =>
    set(
      (state) => {
        state.ui.sidebarOpen = !state.ui.sidebarOpen;
      },
      false,
      'toggleSidebar'
    ),
    
  toggleCommandPalette: () =>
    set(
      (state) => {
        state.ui.commandPaletteOpen = !state.ui.commandPaletteOpen;
      },
      false,
      'toggleCommandPalette'
    ),
  
  // Analytics
  trackEvent: (type, data = {}) =>
    set(
      (state) => {
        state.analytics.events.push({
          type,
          data,
          timestamp: Date.now(),
        });
        
        // Keep only last 1000 events for performance
        if (state.analytics.events.length > 1000) {
          state.analytics.events = state.analytics.events.slice(-1000);
        }
      },
      false,
      'trackEvent'
    ),
    
  clearAnalytics: () =>
    set(
      (state) => {
        state.analytics.events = [];
      },
      false,
      'clearAnalytics'
    ),
  
  // Advanced actions
  optimizePerformance: () => {
    const state = get();
    
    // Auto-disable animations if performance is poor
    if (state.performance.renderTime > 16) {
      set(
        (state) => {
          state.preferences.animationsEnabled = false;
        },
        false,
        'optimizePerformance'
      );
    }
    
    // Clear old notifications
    set(
      (state) => {
        const now = Date.now();
        state.ui.notifications = state.ui.notifications.filter(
          (n) => n.persistent || now - n.timestamp < 30000
        );
      },
      false,
      'optimizePerformance'
    );
  },
  
  adaptToUserBehavior: () => {
    const state = get();
    const recentEvents = state.analytics.events.slice(-100);
    
    // AI-powered behavior adaptation
    const modalOpenEvents = recentEvents.filter((e) => e.type === 'modal_open').length;
    const commandPaletteEvents = recentEvents.filter((e) => e.type === 'command_palette_open').length;
    
    // If user frequently uses command palette, suggest keyboard shortcuts
    if (commandPaletteEvents > 10) {
      set(
        (state) => {
          state.ui.notifications.push({
            id: crypto.randomUUID(),
            type: 'info',
            message: 'Pro tip: Use Ctrl+K for quick access to commands!',
            timestamp: Date.now(),
          });
        },
        false,
        'adaptToUserBehavior'
      );
    }
  },
});

// Create the store with all middleware
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer(createAppStore)
      ),
      {
        name: 'sainpse-app-store',
        partialize: (state) => ({
          preferences: state.preferences,
          analytics: {
            sessionId: state.analytics.sessionId,
            userId: state.analytics.userId,
          },
        }),
      }
    ),
    {
      name: 'Sainpse App Store',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// Selectors for optimized re-renders
export const usePreferences = () => useAppStore((state) => state.preferences);
export const useUIState = () => useAppStore((state) => state.ui);
export const usePerformanceMetrics = () => useAppStore((state) => state.performance);
export const useAnalytics = () => useAppStore((state) => state.analytics);

// Action selectors
export const useAppActions = () => useAppStore((state) => ({
  updatePreferences: state.updatePreferences,
  setLoading: state.setLoading,
  openModal: state.openModal,
  closeModal: state.closeModal,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  trackEvent: state.trackEvent,
  optimizePerformance: state.optimizePerformance,
  adaptToUserBehavior: state.adaptToUserBehavior,
}));
