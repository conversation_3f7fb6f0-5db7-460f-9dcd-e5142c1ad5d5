import React, { Component, ErrorInfo, ReactNode } from 'react';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertTriangle, RefreshCw, Bug, Send, Copy, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useAppStore } from '@/stores/app-store';

interface ErrorInfo {
  error: Error;
  errorInfo: ErrorInfo;
  errorId: string;
  timestamp: number;
  userAgent: string;
  url: string;
  userId?: string;
  sessionId: string;
  componentStack: string;
  errorBoundary: string;
}

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
  errorInfo?: ErrorInfo;
}

// Advanced error reporting service
class ErrorReportingService {
  private static instance: ErrorReportingService;
  private errors: ErrorInfo[] = [];
  
  static getInstance(): ErrorReportingService {
    if (!ErrorReportingService.instance) {
      ErrorReportingService.instance = new ErrorReportingService();
    }
    return ErrorReportingService.instance;
  }
  
  reportError(error: Error, errorInfo: ErrorInfo, context: Record<string, any> = {}): string {
    const errorId = crypto.randomUUID();
    const timestamp = Date.now();
    
    const errorReport: ErrorInfo = {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack || '',
      } as Error,
      errorInfo,
      errorId,
      timestamp,
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: useAppStore.getState().analytics.sessionId,
      userId: useAppStore.getState().analytics.userId,
      componentStack: errorInfo.componentStack || '',
      errorBoundary: context.errorBoundary || 'Unknown',
    };
    
    this.errors.push(errorReport);
    
    // Send to analytics
    useAppStore.getState().trackEvent('error_boundary_triggered', {
      errorId,
      errorName: error.name,
      errorMessage: error.message,
      componentStack: errorInfo.componentStack,
      ...context,
    });
    
    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      this.sendToErrorService(errorReport);
    }
    
    console.error('Error Boundary Triggered:', errorReport);
    
    return errorId;
  }
  
  private async sendToErrorService(errorReport: ErrorInfo): Promise<void> {
    try {
      // Replace with your actual error reporting service
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorReport),
      });
    } catch (err) {
      console.error('Failed to send error report:', err);
    }
  }
  
  getErrors(): ErrorInfo[] {
    return this.errors;
  }
  
  clearErrors(): void {
    this.errors = [];
  }
}

// Modern error fallback component
const ErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary, 
  errorInfo 
}) => {
  const [showDetails, setShowDetails] = React.useState(false);
  const [copied, setCopied] = React.useState(false);
  const addNotification = useAppStore((state) => state.addNotification);
  
  const errorDetails = React.useMemo(() => ({
    name: error.name,
    message: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    componentStack: errorInfo?.componentStack,
  }), [error, errorInfo]);
  
  const copyErrorDetails = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
      setCopied(true);
      addNotification({
        type: 'success',
        message: 'Error details copied to clipboard',
      });
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      addNotification({
        type: 'error',
        message: 'Failed to copy error details',
      });
    }
  };
  
  const reportError = () => {
    const errorService = ErrorReportingService.getInstance();
    const errorId = errorService.reportError(error, errorInfo as any, {
      errorBoundary: 'ErrorFallback',
    });
    
    addNotification({
      type: 'success',
      message: `Error reported with ID: ${errorId.slice(0, 8)}`,
    });
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950 dark:to-red-900"
    >
      <Card className="w-full max-w-2xl shadow-2xl border-red-200 dark:border-red-800">
        <CardHeader className="text-center">
          <motion.div
            initial={{ rotate: 0 }}
            animate={{ rotate: [0, -10, 10, -10, 0] }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mx-auto w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mb-4"
          >
            <AlertTriangle className="w-8 h-8 text-red-600 dark:text-red-400" />
          </motion.div>
          
          <CardTitle className="text-2xl font-bold text-red-800 dark:text-red-200">
            Oops! Something went wrong
          </CardTitle>
          
          <CardDescription className="text-red-600 dark:text-red-400">
            We encountered an unexpected error. Don't worry, we've been notified and are working on a fix.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Error Summary */}
          <div className="bg-red-50 dark:bg-red-950/50 p-4 rounded-lg border border-red-200 dark:border-red-800">
            <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">
              Error Summary
            </h3>
            <p className="text-sm text-red-700 dark:text-red-300 font-mono">
              {error.name}: {error.message}
            </p>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={resetErrorBoundary}
              className="flex-1 min-w-[120px]"
              variant="default"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
            
            <Button
              onClick={reportError}
              variant="outline"
              className="flex-1 min-w-[120px]"
            >
              <Send className="w-4 h-4 mr-2" />
              Report Error
            </Button>
            
            <Button
              onClick={copyErrorDetails}
              variant="outline"
              className="flex-1 min-w-[120px]"
            >
              <Copy className="w-4 h-4 mr-2" />
              {copied ? 'Copied!' : 'Copy Details'}
            </Button>
          </div>
          
          {/* Collapsible Error Details */}
          <Collapsible open={showDetails} onOpenChange={setShowDetails}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between">
                <span className="flex items-center">
                  <Bug className="w-4 h-4 mr-2" />
                  Technical Details
                </span>
                {showDetails ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            
            <CollapsibleContent>
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-4 p-4 bg-gray-50 dark:bg-gray-900 rounded-lg border"
              >
                <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-auto max-h-64 whitespace-pre-wrap">
                  {JSON.stringify(errorDetails, null, 2)}
                </pre>
              </motion.div>
            </CollapsibleContent>
          </Collapsible>
          
          {/* Help Text */}
          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
            <p>
              If this problem persists, please{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-blue-600 dark:text-blue-400 hover:underline"
              >
                contact our support team
              </a>
              {' '}with the error details above.
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

// Enhanced Error Boundary component
interface EnhancedErrorBoundaryProps {
  children: ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  isolate?: boolean;
  name?: string;
}

export const EnhancedErrorBoundary: React.FC<EnhancedErrorBoundaryProps> = ({
  children,
  fallback: FallbackComponent = ErrorFallback,
  onError,
  isolate = false,
  name = 'EnhancedErrorBoundary',
}) => {
  const handleError = React.useCallback((error: Error, errorInfo: ErrorInfo) => {
    const errorService = ErrorReportingService.getInstance();
    errorService.reportError(error, errorInfo, {
      errorBoundary: name,
      isolate,
    });
    
    onError?.(error, errorInfo);
  }, [onError, name, isolate]);
  
  return (
    <ReactErrorBoundary
      FallbackComponent={FallbackComponent}
      onError={handleError}
      isolate={isolate}
    >
      <AnimatePresence mode="wait">
        {children}
      </AnimatePresence>
    </ReactErrorBoundary>
  );
};

// Hook for manual error reporting
export const useErrorReporting = () => {
  const addNotification = useAppStore((state) => state.addNotification);
  
  const reportError = React.useCallback((error: Error, context?: Record<string, any>) => {
    const errorService = ErrorReportingService.getInstance();
    const errorId = errorService.reportError(error, {} as ErrorInfo, {
      ...context,
      manual: true,
    });
    
    addNotification({
      type: 'error',
      message: `Error reported: ${error.message}`,
    });
    
    return errorId;
  }, [addNotification]);
  
  const getErrorHistory = React.useCallback(() => {
    const errorService = ErrorReportingService.getInstance();
    return errorService.getErrors();
  }, []);
  
  const clearErrorHistory = React.useCallback(() => {
    const errorService = ErrorReportingService.getInstance();
    errorService.clearErrors();
  }, []);
  
  return {
    reportError,
    getErrorHistory,
    clearErrorHistory,
  };
};

// Global error handler setup
export const setupGlobalErrorHandling = () => {
  const errorService = ErrorReportingService.getInstance();
  
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = new Error(event.reason?.message || 'Unhandled Promise Rejection');
    error.stack = event.reason?.stack;
    
    errorService.reportError(error, {} as ErrorInfo, {
      type: 'unhandledrejection',
      reason: event.reason,
    });
  });
  
  // Handle global errors
  window.addEventListener('error', (event) => {
    const error = new Error(event.message);
    error.stack = event.error?.stack;
    
    errorService.reportError(error, {} as ErrorInfo, {
      type: 'global_error',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
    });
  });
};

export default EnhancedErrorBoundary;
