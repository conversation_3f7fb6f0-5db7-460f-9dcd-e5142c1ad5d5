import React, { useRef, useEffect } from 'react';

const NetworkAnimation = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mousePos = useRef<{ x: number | null; y: number | null }>({ x: null, y: null });
  const scrollMultiplier = useRef(1);
  const pulseRef = useRef(0);
  const pulseDirectionRef = useRef(1);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    let animationFrameId: number;

    // Adjust canvas size to parent
    const setSize = () => {
      canvas.width = canvas.clientWidth;
      canvas.height = canvas.clientHeight;
    };
    setSize();
    window.addEventListener('resize', setSize);

    // Make scroll effect stronger
    const handleScroll = () => {
      scrollMultiplier.current = 1 + window.scrollY / 500; // Increased sensitivity
    };
    window.addEventListener('scroll', handleScroll);

    // Track mouse position over canvas
    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mousePos.current.x = e.clientX - rect.left;
      mousePos.current.y = e.clientY - rect.top;
    };
    canvas.addEventListener('mousemove', handleMouseMove);

    const handleMouseLeave = () => {
      mousePos.current.x = null;
      mousePos.current.y = null;
    };
    canvas.addEventListener('mouseleave', handleMouseLeave);

    const width = canvas.width, height = canvas.height;
    const numParticles = 80; // Increased particles for denser network
    const particles = Array.from({ length: numParticles }, () => ({
      x: Math.random() * width,
      y: Math.random() * height,
      vx: (Math.random() - 0.5) * 0.6,
      vy: (Math.random() - 0.5) * 0.6,
      size: Math.random() * 2 + 1, // Variable particle size
      baseSize: Math.random() * 2 + 1, // Store original size
    }));

    const draw = () => {
      if (!ctx) return;

      // Update pulse value
      pulseRef.current += 0.02 * pulseDirectionRef.current;
      if (pulseRef.current >= 1) {
        pulseRef.current = 1;
        pulseDirectionRef.current = -1;
      } else if (pulseRef.current <= 0.3) {
        pulseRef.current = 0.3;
        pulseDirectionRef.current = 1;
      }

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Move and draw particles with scroll multiplier effect
      particles.forEach(p => {
        p.x += p.vx * scrollMultiplier.current;
        p.y += p.vy * scrollMultiplier.current;
        if (p.x < 0 || p.x > canvas.width) p.vx = -p.vx;
        if (p.y < 0 || p.y > canvas.height) p.vy = -p.vy;

        // Dynamic particle size and glow based on mouse proximity
        if (mousePos.current.x !== null && mousePos.current.y !== null) {
          const dx = p.x - mousePos.current.x;
          const dy = p.y - mousePos.current.y;
          const dist = Math.sqrt(dx * dx + dy * dy);
          if (dist < 150) {
            const scale = 1 + (150 - dist) / 150;
            p.size = p.baseSize * scale;
            
            // Add glow effect
            ctx.shadowBlur = 15;
            ctx.shadowColor = 'rgba(16,185,129,0.5)';
          } else {
            p.size = p.baseSize;
            ctx.shadowBlur = 0;
          }
        }

        ctx.beginPath();
        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(16,185,129,1)'; // Emerald-500
        ctx.fill();

        // When mouse is near, add a repulsion force and draw a connecting line
        if (mousePos.current.x !== null && mousePos.current.y !== null) {
          const dx = p.x - mousePos.current.x;
          const dy = p.y - mousePos.current.y;
          const dist = Math.sqrt(dx * dx + dy * dy);
          if (dist < 150 && dist > 0) {
            // Draw connection line
            ctx.beginPath();
            ctx.moveTo(p.x, p.y);
            ctx.lineTo(mousePos.current.x, mousePos.current.y);
            ctx.strokeStyle = `rgba(16,185,129,${1 - dist / 150})`;
            ctx.stroke();
            // Apply a repulsive force
            const force = (150 - dist) / 150 * 0.05;
            p.vx += (dx / dist) * force;
            p.vy += (dy / dist) * force;
          }
        }
      });

      // Draw connections between particles using a larger threshold (150px)
      for (let i = 0; i < particles.length; i++) {
        for (let j = i + 1; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x;
          const dy = particles[i].y - particles[j].y;
          const dist = Math.sqrt(dx * dx + dy * dy);
          if (dist < 150) {
            ctx.beginPath();
            ctx.moveTo(particles[i].x, particles[i].y);
            ctx.lineTo(particles[j].x, particles[j].y);
            ctx.strokeStyle = `rgba(16,185,129,${(1 - dist / 150) * pulseRef.current})`;
            ctx.stroke();
          }
        }
      }

      animationFrameId = requestAnimationFrame(draw);
    };

    draw();
    return () => {
      cancelAnimationFrame(animationFrameId);
      window.removeEventListener('resize', setSize);
      window.removeEventListener('scroll', handleScroll);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return <canvas ref={canvasRef} className="w-full h-full block" />;
};

export default NetworkAnimation;