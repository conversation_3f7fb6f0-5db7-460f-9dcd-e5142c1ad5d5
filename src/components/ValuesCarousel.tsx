import React, { useState, useEffect, useRef } from 'react';
import { Brain, Lightbulb, Minimize2, Users, Zap } from 'lucide-react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';

const values = [
  { icon: Users, title: 'Collaboration', description: 'We believe in the power of collective effort, where individuals, businesses, and communities work together in harmony to achieve extraordinary outcomes and unlock emergent intelligence.' },
  { icon: Lightbulb, title: 'Innovation', description: 'We are committed to fostering groundbreaking ideas, leveraging cutting-edge technologies, and pushing the boundaries of what’s possible to drive transformative progress.' },
  { icon: Minimize2, title: 'Simplicity', description: 'We value clarity and ease, striving to simplify complex systems and solutions to make intelligence and progress accessible to all.' },
  { icon: Zap, title: 'Empowerment', description: 'We strive to enable individuals, organizations, and communities to realize their full potential by providing tools, knowledge, and opportunities to create lasting impact.' },
  { icon: Brain, title: 'Emergence', description: 'We embrace the philosophy that intelligence arises not only through advanced technologies but through systems, networks, and relationships working in unity to achieve more together.' },
];

const ValuesCarousel = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout>();
  const constraintsRef = useRef(null);

  const startInterval = () => {
    intervalRef.current = setInterval(() => {
      setActiveIndex(prev => (prev + 1) % values.length);
    }, 5000);
  };

  useEffect(() => {
    startInterval();
    return () => clearInterval(intervalRef.current);
  }, []);

  const handleMouseEnter = () => {
    clearInterval(intervalRef.current);
  };

  const handleMouseLeave = () => {
    startInterval();
  };

  const handleDragEnd = (event: any, info: PanInfo) => {
    const swipeThreshold = 50;
    if (info.offset.x < -swipeThreshold && activeIndex < values.length - 1) {
      setDirection(1);
      setActiveIndex(prev => prev + 1);
    } else if (info.offset.x > swipeThreshold && activeIndex > 0) {
      setDirection(-1);
      setActiveIndex(prev => prev - 1);
    }
  };

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="py-20 bg-midnight/50 overflow-hidden relative"
    >
      {/* Add animated particles in background */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-emerald-500/20 rounded-full"
            initial={{ 
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight 
            }}
            animate={{
              x: Math.random() * window.innerWidth,
              y: Math.random() * window.innerHeight,
              scale: [1, 1.5, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: Math.random() * 10 + 10,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        ))}
      </div>

      <motion.h2 
        className="text-3xl md:text-5xl font-bold text-center mb-12 relative"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        Our Values
      </motion.h2>

      <div 
        ref={constraintsRef}
        className="relative w-full max-w-4xl mx-auto perspective-1000"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <AnimatePresence initial={false} mode="wait">
          <motion.div
            key={activeIndex}
            drag="x"
            dragConstraints={{ left: 0, right: 0 }}
            onDragEnd={handleDragEnd}
            initial={{ 
              rotateY: direction > 0 ? 90 : -90,
              x: direction > 0 ? 1000 : -1000,
              opacity: 0,
              scale: 0.8
            }}
            animate={{ 
              rotateY: 0,
              x: 0,
              opacity: 1,
              scale: 1
            }}
            exit={{ 
              rotateY: direction > 0 ? -90 : 90,
              x: direction > 0 ? -1000 : 1000,
              opacity: 0,
              scale: 0.8
            }}
            transition={{
              type: "spring",
              stiffness: 300,
              damping: 30
            }}
            className="px-4"
          >
            <motion.div
              whileHover={{ scale: 1.02, rotateY: [-1, 1] }}
              transition={{ rotateY: { repeat: Infinity, duration: 2 } }}
              className="bg-gradient-to-br from-midnight/80 to-midnight/40 p-8 md:p-12 rounded-2xl border border-emerald-500/20 hover:border-emerald-500/50 transition-all duration-300 backdrop-blur-sm shadow-xl hover:shadow-emerald-500/10"
            >
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  type: "spring",
                  stiffness: 200,
                  damping: 20,
                  delay: 0.2 
                }}
                className="relative"
              >
                {React.createElement(values[activeIndex].icon, {
                  className: "w-16 h-16 md:w-20 md:h-20 text-emerald-500 mb-6 mx-auto filter drop-shadow-glow animate-float"
                })}
              </motion.div>
              <motion.h3 
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.3 }}
                className="text-2xl md:text-3xl font-semibold mb-4 text-center"
              >
                {values[activeIndex].title}
              </motion.h3>
              <motion.p 
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-snow/80 text-center text-lg md:text-xl max-w-2xl mx-auto leading-relaxed"
              >
                {values[activeIndex].description}
              </motion.p>
            </motion.div>
          </motion.div>
        </AnimatePresence>

        <div className="flex justify-center mt-8 space-x-3">
          {values.map((_, index) => (
            <motion.button 
              key={index}
              onClick={() => {
                setDirection(index > activeIndex ? 1 : -1);
                setActiveIndex(index);
              }}
              className={`w-4 h-4 rounded-full transition-all duration-300 relative overflow-hidden
                ${activeIndex === index ? 'bg-emerald-500 scale-125' : 'bg-snow/30 hover:bg-snow/50'}`}
              whileHover={{ scale: 1.3 }}
              whileTap={{ scale: 0.9 }}
            >
              {activeIndex === index && (
                <motion.div
                  className="absolute inset-0 bg-emerald-400/50"
                  layoutId="dot"
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 30
                  }}
                />
              )}
            </motion.button>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default ValuesCarousel;
