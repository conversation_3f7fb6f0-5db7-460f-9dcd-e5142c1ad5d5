import React, { useState, useEffect, useCallback, useMemo, useRef, Suspense, startTransition } from 'react';
import { createClient } from '@supabase/supabase-js';
import { motion, AnimatePresence, useScroll, useTransform, useSpring, useMotionValue, useVelocity } from 'framer-motion';
import { Cpu, Zap, Brain, Sparkles, Eye, Palette, Volume2, VolumeX } from 'lucide-react';
import Hero from '../components/Hero';
import Mission from '../components/Mission';
import ValuesCarousel from '../components/ValuesCarousel';
import Services from '../components/Services';
import InnovationLab from '../components/InnovationLab';
import Footer from '../components/Footer';
import { Auth } from '../components/Auth';
import { Dialog, DialogContent } from '../components/ui/dialog';
import { useNavigate, Link } from 'react-router-dom';

// Advanced Types for AI-Enhanced Features
interface QuantumParticle {
  id: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  life: number;
  maxLife: number;
  color: string;
  size: number;
}

interface NeuralConnection {
  from: { x: number; y: number };
  to: { x: number; y: number };
  strength: number;
  active: boolean;
}

interface AdaptiveTheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  intensity: number;
}

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_ANON_KEY
);

// Quantum Particle System for Neural Network Visualization
const useQuantumParticles = (count: number = 50) => {
  const [particles, setParticles] = useState<QuantumParticle[]>([]);
  const animationRef = useRef<number>();

  const createParticle = useCallback((): QuantumParticle => ({
    id: Math.random().toString(36).substring(2, 11),
    x: Math.random() * window.innerWidth,
    y: Math.random() * window.innerHeight,
    vx: (Math.random() - 0.5) * 2,
    vy: (Math.random() - 0.5) * 2,
    life: Math.random() * 100,
    maxLife: 100 + Math.random() * 100,
    color: `hsl(${142 + Math.random() * 60}, 84%, ${50 + Math.random() * 30}%)`,
    size: 1 + Math.random() * 3,
  }), []);

  const updateParticles = useCallback(() => {
    setParticles(prev => prev.map(particle => {
      const newParticle = {
        ...particle,
        x: particle.x + particle.vx,
        y: particle.y + particle.vy,
        life: particle.life + 1,
      };

      // Boundary wrapping
      if (newParticle.x < 0) newParticle.x = window.innerWidth;
      if (newParticle.x > window.innerWidth) newParticle.x = 0;
      if (newParticle.y < 0) newParticle.y = window.innerHeight;
      if (newParticle.y > window.innerHeight) newParticle.y = 0;

      // Regenerate if life exceeded
      if (newParticle.life > newParticle.maxLife) {
        return createParticle();
      }

      return newParticle;
    }));
  }, [createParticle]);

  useEffect(() => {
    // Initialize particles
    setParticles(Array.from({ length: count }, createParticle));

    const animate = () => {
      updateParticles();
      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [count, createParticle, updateParticles]);

  return particles;
};

// AI-Powered Adaptive Theme Hook
const useAdaptiveTheme = () => {
  const [theme, setTheme] = useState<AdaptiveTheme>({
    primary: 'rgb(16, 185, 129)',
    secondary: 'rgb(0, 255, 128)',
    accent: 'rgb(52, 211, 153)',
    background: 'rgb(8, 8, 12)',
    intensity: 1,
  });

  useEffect(() => {
    const updateTheme = () => {
      const hour = new Date().getHours();
      const isNight = hour < 6 || hour > 20;
      const scrollY = window.scrollY;
      const intensity = Math.min(1 + scrollY / 1000, 2);

      setTheme(prev => ({
        ...prev,
        intensity,
        primary: isNight
          ? `rgb(${16 * intensity}, ${185 * intensity}, ${129 * intensity})`
          : `rgb(${20 * intensity}, ${200 * intensity}, ${140 * intensity})`,
      }));
    };

    updateTheme();
    window.addEventListener('scroll', updateTheme);
    const interval = setInterval(updateTheme, 60000); // Update every minute

    return () => {
      window.removeEventListener('scroll', updateTheme);
      clearInterval(interval);
    };
  }, []);

  return theme;
};

const Index = () => {
  // Core State
  const [showAuth, setShowAuth] = useState(false);
  const [user, setUser] = useState(null);
  const [profile, setProfile] = useState(null);
  const [authLoading, setAuthLoading] = useState(true);
  const [menuOpen, setMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // AI-Enhanced Features
  const [neuralMode, setNeuralMode] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(false);
  const [aiPersonality, setAiPersonality] = useState<'minimal' | 'dynamic' | 'quantum'>('dynamic');
  const [focusMode, setFocusMode] = useState(false);

  // Advanced Hooks
  const navigate = useNavigate();
  const particles = useQuantumParticles(neuralMode ? 100 : 30);
  const adaptiveTheme = useAdaptiveTheme();
  const containerRef = useRef<HTMLDivElement>(null);

  // Framer Motion Advanced Hooks
  const { scrollY, scrollYProgress } = useScroll();
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const mouseVelocity = useVelocity(scrollY);

  // Transform values for parallax and dynamic effects
  const backgroundY = useTransform(scrollY, [0, 1000], [0, -200]);
  const headerOpacity = useTransform(scrollYProgress, [0, 0.1], [1, 0.8]);
  const dynamicScale = useSpring(useTransform(mouseVelocity, [-1000, 1000], [0.95, 1.05]), {
    stiffness: 300,
    damping: 30
  });

  // Enhanced Mouse Tracking for Neural Interactions
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      mouseX.set(e.clientX - rect.left);
      mouseY.set(e.clientY - rect.top);
    }
  }, [mouseX, mouseY]);

  // AI-Powered User Authentication with Enhanced UX
  useEffect(() => {
    const fetchUser = async () => {
      try {
        startTransition(() => {
          setAuthLoading(true);
        });

        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUser(user);
          const { data: profile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', user.id)
            .single();
          setProfile(profile);

          // AI Personality Detection based on user data
          if (profile?.preferences) {
            setAiPersonality(profile.preferences.personality || 'dynamic');
          }
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        startTransition(() => {
          setAuthLoading(false);
        });
      }
    };

    fetchUser();

    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      if (session?.user) {
        setUser(session.user);
      } else {
        setUser(null);
        setProfile(null);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  // Advanced Scroll Detection with Neural Network Activation
  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 50;
      setIsScrolled(scrolled);

      // Activate neural mode based on scroll velocity
      const velocity = Math.abs(mouseVelocity.get());
      if (velocity > 500 && !neuralMode) {
        setNeuralMode(true);
        setTimeout(() => setNeuralMode(false), 3000);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('mousemove', handleMouseMove, { passive: true });

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [handleMouseMove, mouseVelocity, neuralMode]);

  // Quantum Sound System
  useEffect(() => {
    if (soundEnabled && typeof window !== 'undefined') {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      const playQuantumTone = (frequency: number, duration: number) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0, audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
      };

      const handleInteraction = () => {
        if (Math.random() > 0.7) { // 30% chance
          playQuantumTone(200 + Math.random() * 400, 0.1);
        }
      };

      document.addEventListener('click', handleInteraction);
      return () => {
        document.removeEventListener('click', handleInteraction);
        audioContext.close();
      };
    }
  }, [soundEnabled]);

  // Keyboard Shortcuts for Power Users
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'n':
            e.preventDefault();
            setNeuralMode(prev => !prev);
            break;
          case 'm':
            e.preventDefault();
            setSoundEnabled(prev => !prev);
            break;
          case 'f':
            e.preventDefault();
            setFocusMode(prev => !prev);
            break;
          case 'q':
            e.preventDefault();
            setAiPersonality(prev =>
              prev === 'minimal' ? 'dynamic' :
              prev === 'dynamic' ? 'quantum' : 'minimal'
            );
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  // Enhanced Navigation with AI-Powered Descriptions
  const navLinks = useMemo(() => [
    {
      href: '#mission',
      label: 'About Us',
      icon: Brain,
      description: 'Discover our neural-inspired mission',
      aiHint: 'Learn about our synaptic approach to intelligence'
    },
    {
      href: '#services',
      label: 'Services',
      icon: Cpu,
      description: 'Explore our quantum-enhanced offerings',
      aiHint: 'Dive into our AI-powered solutions'
    },
    {
      href: '/education',
      label: 'Education',
      icon: Sparkles,
      description: 'Unlock knowledge through adaptive learning',
      aiHint: 'Experience personalized learning paths'
    },
    {
      href: '#footer',
      label: 'Contact Us',
      icon: Zap,
      description: 'Connect through multiple dimensions',
      aiHint: 'Establish neural connections with our team'
    },
  ], []);

  // Quantum-Inspired Animation Variants
  const quantumHeaderVariants = useMemo(() => ({
    hidden: {
      y: -100,
      opacity: 0,
      scale: 0.9,
      rotateX: -15,
      filter: "blur(10px)"
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      rotateX: 0,
      filter: "blur(0px)",
      transition: {
        duration: 1.2,
        ease: [0.22, 1, 0.36, 1],
        staggerChildren: 0.08,
        delayChildren: 0.1
      }
    }
  }), []);

  const neuralItemVariants = useMemo(() => ({
    hidden: {
      y: -20,
      opacity: 0,
      scale: 0.8,
      rotateY: neuralMode ? 180 : 0,
      filter: "hue-rotate(0deg)"
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      rotateY: 0,
      filter: neuralMode ? "hue-rotate(120deg)" : "hue-rotate(0deg)",
      transition: {
        duration: 0.8,
        ease: "easeOut",
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  }), [neuralMode]);

  const quantumLogoVariants = useMemo(() => ({
    hidden: {
      scale: 0.8,
      opacity: 0,
      rotate: -180,
      filter: "brightness(0.5)"
    },
    visible: {
      scale: 1,
      opacity: 1,
      rotate: 0,
      filter: "brightness(1)",
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15,
        duration: 1.5
      }
    }
  }), []);

  const synapticLetterAnimation = useMemo(() => ({
    rest: {
      y: 0,
      scale: 1,
      color: adaptiveTheme.primary,
      textShadow: "0 0 0px rgba(16, 185, 129, 0)",
      transition: {
        duration: 0.2,
        type: "tween",
        ease: "easeIn"
      }
    },
    hover: {
      y: -5,
      scale: 1.1,
      color: adaptiveTheme.secondary,
      textShadow: `0 0 20px ${adaptiveTheme.accent}`,
      transition: {
        duration: 0.2,
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    }
  }), [adaptiveTheme]);

  // Neural Network Connection Generator
  const generateNeuralConnections = useCallback((): NeuralConnection[] => {
    if (!neuralMode) return [];

    return Array.from({ length: 15 }, () => ({
      from: {
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight
      },
      to: {
        x: Math.random() * window.innerWidth,
        y: Math.random() * window.innerHeight
      },
      strength: Math.random(),
      active: Math.random() > 0.5
    }));
  }, [neuralMode]);

  const neuralConnections = useMemo(generateNeuralConnections, [generateNeuralConnections]);

  // Enhanced Navigation with Neural Feedback
  const handleNavClick = useCallback((e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    setMenuOpen(false);

    // Neural activation on navigation
    if (neuralMode) {
      setNeuralMode(false);
      setTimeout(() => setNeuralMode(true), 100);
    }

    // Quantum sound feedback
    if (soundEnabled && typeof window !== 'undefined') {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);
      oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.1);

      oscillator.start();
      oscillator.stop(audioContext.currentTime + 0.1);
    }

    if (href.startsWith('#')) {
      const element = document.querySelector(href);
      if (element) {
        setTimeout(() => {
          const offsetTop = element.getBoundingClientRect().top + window.pageYOffset - 100;
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });
        }, 150);
      }
    } else {
      navigate(href);
    }
  }, [navigate, neuralMode, soundEnabled]);

  // Quantum Particle System Component
  const QuantumParticleSystem = useMemo(() => {
    if (!neuralMode && particles.length === 0) return null;

    return (
      <div className="fixed inset-0 pointer-events-none z-10 overflow-hidden">
        <svg width="100%" height="100%" className="absolute inset-0">
          {/* Neural Connections */}
          {neuralConnections.map((connection, index) => (
            <motion.line
              key={`connection-${index}`}
              x1={connection.from.x}
              y1={connection.from.y}
              x2={connection.to.x}
              y2={connection.to.y}
              stroke={adaptiveTheme.accent}
              strokeWidth={connection.strength * 2}
              opacity={connection.active ? 0.6 : 0.2}
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, delay: index * 0.1 }}
            />
          ))}

          {/* Quantum Particles */}
          {particles.map((particle) => (
            <motion.circle
              key={particle.id}
              cx={particle.x}
              cy={particle.y}
              r={particle.size}
              fill={particle.color}
              initial={{ scale: 0, opacity: 0 }}
              animate={{
                scale: 1,
                opacity: 1 - (particle.life / particle.maxLife),
                filter: `blur(${particle.life / particle.maxLife}px)`
              }}
              transition={{ duration: 0.5 }}
            />
          ))}
        </svg>
      </div>
    );
  }, [neuralMode, particles, neuralConnections, adaptiveTheme]);

  // AI Control Panel Component
  const AIControlPanel = useMemo(() => (
    <motion.div
      className="fixed top-20 right-4 z-40 flex flex-col space-y-2"
      initial={{ x: 100, opacity: 0 }}
      animate={{ x: 0, opacity: focusMode ? 0.3 : 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.button
        onClick={() => setNeuralMode(prev => !prev)}
        className={`p-3 rounded-full backdrop-blur-md transition-all duration-300 ${
          neuralMode
            ? 'bg-emerald-500/20 text-emerald-400 shadow-lg shadow-emerald-500/20'
            : 'bg-white/10 text-white/70 hover:bg-white/20'
        }`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Toggle Neural Mode (Ctrl+N)"
      >
        <Brain className="w-5 h-5" />
      </motion.button>

      <motion.button
        onClick={() => setSoundEnabled(prev => !prev)}
        className={`p-3 rounded-full backdrop-blur-md transition-all duration-300 ${
          soundEnabled
            ? 'bg-emerald-500/20 text-emerald-400 shadow-lg shadow-emerald-500/20'
            : 'bg-white/10 text-white/70 hover:bg-white/20'
        }`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Toggle Quantum Audio (Ctrl+M)"
      >
        {soundEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
      </motion.button>

      <motion.button
        onClick={() => setFocusMode(prev => !prev)}
        className={`p-3 rounded-full backdrop-blur-md transition-all duration-300 ${
          focusMode
            ? 'bg-emerald-500/20 text-emerald-400 shadow-lg shadow-emerald-500/20'
            : 'bg-white/10 text-white/70 hover:bg-white/20'
        }`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title="Toggle Focus Mode (Ctrl+F)"
      >
        <Eye className="w-5 h-5" />
      </motion.button>

      <motion.button
        onClick={() => setAiPersonality(prev =>
          prev === 'minimal' ? 'dynamic' :
          prev === 'dynamic' ? 'quantum' : 'minimal'
        )}
        className="p-3 rounded-full backdrop-blur-md bg-white/10 text-white/70 hover:bg-white/20 transition-all duration-300"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        title={`AI Personality: ${aiPersonality} (Ctrl+Q)`}
      >
        <Palette className="w-5 h-5" />
      </motion.button>
    </motion.div>
  ), [neuralMode, soundEnabled, focusMode, aiPersonality]);

  return (
    <motion.div
      ref={containerRef}
      className="min-h-screen bg-midnight text-snow relative overflow-hidden"
      style={{
        scale: dynamicScale,
        background: `linear-gradient(135deg, ${adaptiveTheme.background} 0%, rgba(8, 8, 12, 0.95) 100%)`
      }}
    >
      {/* Quantum Particle System */}
      <Suspense fallback={null}>
        {QuantumParticleSystem}
      </Suspense>

      {/* AI Control Panel */}
      {AIControlPanel}

      {/* Enhanced Header with Neural Network Styling */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={quantumHeaderVariants}
        style={{ opacity: headerOpacity, y: backgroundY }}
        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
          isScrolled
            ? 'bg-gradient-to-r from-white/15 via-white/10 to-white/15 backdrop-blur-xl shadow-2xl border-b border-emerald-500/20'
            : 'bg-gradient-to-r from-white/8 via-white/5 to-white/8 backdrop-blur-lg'
        } ${focusMode ? 'opacity-30' : 'opacity-100'}`}
      >
        <div className="container mx-auto px-6 py-4 flex items-center justify-between">
          <motion.div
            variants={quantumLogoVariants}
            className="flex items-center group"
            whileHover={{ scale: 1.05 }}
          >
            <motion.img
              src="/latest_logo2_black_NObg.png"
              alt="Sainpse Logo"
              className="w-10 h-10 mr-2 filter brightness-0 invert"
              animate={{
                rotate: neuralMode ? [0, 360] : 0,
                filter: neuralMode
                  ? ["brightness(0) invert(1)", "brightness(1) invert(0) hue-rotate(120deg)", "brightness(0) invert(1)"]
                  : "brightness(0) invert(1)"
              }}
              transition={{
                rotate: { duration: 2, repeat: neuralMode ? Infinity : 0 },
                filter: { duration: 1, repeat: neuralMode ? Infinity : 0 }
              }}
            />
            <motion.h1
              className="text-2xl font-bold tracking-tight bg-gradient-to-r from-snow via-emerald-300 to-snow bg-clip-text text-transparent"
              whileHover="hover"
              initial="rest"
            >
              {"Sainpse".split("").map((letter, index) => (
                <motion.span
                  key={index}
                  variants={synapticLetterAnimation}
                  style={{ display: "inline-block" }}
                  custom={index}
                  whileHover={{
                    y: -8,
                    scale: 1.2,
                    rotate: Math.random() * 20 - 10,
                    transition: { duration: 0.2 }
                  }}
                >
                  {letter}
                </motion.span>
              ))}
            </motion.h1>
          </motion.div>

          {/* Enhanced Neural Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link, index) => {
              const IconComponent = link.icon;
              return link.href.startsWith('#') ? (
                <motion.a
                  key={link.href}
                  variants={neuralItemVariants}
                  href={link.href}
                  onClick={(e) => handleNavClick(e, link.href)}
                  className="group relative flex items-center space-x-2 text-sm font-medium transition-all duration-300 hover:text-emerald-400"
                  custom={index}
                  whileHover={{
                    scale: 1.08,
                    y: -2,
                    transition: { duration: 0.2, type: "spring", stiffness: 400 }
                  }}
                  title={aiPersonality === 'quantum' ? link.aiHint : link.description}
                >
                  <motion.div
                    className="p-1 rounded-full bg-gradient-to-r from-emerald-500/20 to-transparent"
                    whileHover={{
                      scale: 1.2,
                      backgroundColor: "rgba(16, 185, 129, 0.3)",
                      boxShadow: "0 0 20px rgba(16, 185, 129, 0.4)"
                    }}
                  >
                    <IconComponent className="w-4 h-4" />
                  </motion.div>

                  <motion.span
                    className="inline-block relative"
                    whileHover={{
                      textShadow: `0 0 10px ${adaptiveTheme.accent}`,
                      filter: "brightness(1.2)"
                    }}
                  >
                    {link.label}
                    <motion.span
                      className="absolute left-0 -bottom-1 w-full h-0.5 bg-gradient-to-r from-transparent via-emerald-500 to-transparent origin-left"
                      initial={{ scaleX: 0 }}
                      whileHover={{ scaleX: 1 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                    />
                  </motion.span>

                  {/* Neural connection lines */}
                  {neuralMode && (
                    <motion.div
                      className="absolute -top-2 -right-2 w-1 h-1 bg-emerald-400 rounded-full"
                      animate={{
                        scale: [1, 1.5, 1],
                        opacity: [0.5, 1, 0.5]
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: index * 0.2
                      }}
                    />
                  )}
                </motion.a>
              ) : (
                <motion.div
                  key={link.href}
                  variants={neuralItemVariants}
                  custom={index}
                >
                  <Link
                    to={link.href}
                    className="group relative flex items-center space-x-2 text-sm font-medium transition-all duration-300 hover:text-emerald-400"
                    title={aiPersonality === 'quantum' ? link.aiHint : link.description}
                  >
                    <motion.div
                      className="p-1 rounded-full bg-gradient-to-r from-emerald-500/20 to-transparent"
                      whileHover={{
                        scale: 1.2,
                        backgroundColor: "rgba(16, 185, 129, 0.3)",
                        boxShadow: "0 0 20px rgba(16, 185, 129, 0.4)"
                      }}
                    >
                      <IconComponent className="w-4 h-4" />
                    </motion.div>

                    <motion.span
                      className="inline-block relative"
                      whileHover={{
                        y: -2,
                        textShadow: `0 0 10px ${adaptiveTheme.accent}`,
                        filter: "brightness(1.2)"
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      {link.label}
                      <motion.span
                        className="absolute left-0 -bottom-1 w-full h-0.5 bg-gradient-to-r from-transparent via-emerald-500 to-transparent origin-left"
                        initial={{ scaleX: 0 }}
                        whileHover={{ scaleX: 1 }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                      />
                    </motion.span>
                  </Link>
                </motion.div>
              );
            })}
          </nav>

          {/* Quantum Mobile Menu Button */}
          <motion.button
            variants={neuralItemVariants}
            className="md:hidden text-snow focus:outline-none p-2 rounded-full backdrop-blur-sm bg-white/10 hover:bg-white/20 transition-all duration-300"
            onClick={() => setMenuOpen(!menuOpen)}
            whileHover={{ scale: 1.1, rotate: 90 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              animate={{ rotate: menuOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {menuOpen ? (
                <motion.path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ duration: 0.3 }}
                />
              ) : (
                <motion.g>
                  <motion.path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 0.2, delay: 0 }}
                  />
                  <motion.path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 12h16"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 0.2, delay: 0.1 }}
                  />
                  <motion.path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 18h16"
                    initial={{ pathLength: 0 }}
                    animate={{ pathLength: 1 }}
                    transition={{ duration: 0.2, delay: 0.2 }}
                  />
                </motion.g>
              )}
            </motion.svg>
          </motion.button>
        </div>

        {/* Enhanced Mobile Menu with Neural Styling */}
        <AnimatePresence>
          {menuOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0, y: -20 }}
              animate={{ opacity: 1, height: "auto", y: 0 }}
              exit={{ opacity: 0, height: 0, y: -20 }}
              transition={{ duration: 0.4, ease: [0.22, 1, 0.36, 1] }}
              className="md:hidden bg-gradient-to-b from-white/15 to-white/5 backdrop-blur-xl border-t border-emerald-500/30"
            >
              <nav className="flex flex-col space-y-2 p-6">
                {navLinks.map((link, index) => {
                  const IconComponent = link.icon;
                  return link.href.startsWith('#') ? (
                    <motion.a
                      key={link.href}
                      initial={{ x: -30, opacity: 0, scale: 0.9 }}
                      animate={{ x: 0, opacity: 1, scale: 1 }}
                      exit={{ x: -30, opacity: 0, scale: 0.9 }}
                      transition={{
                        delay: index * 0.1,
                        duration: 0.3,
                        type: "spring",
                        stiffness: 200
                      }}
                      href={link.href}
                      onClick={(e) => handleNavClick(e, link.href)}
                      className="group relative flex items-center space-x-3 text-base font-medium transition-all duration-300 hover:text-emerald-400 px-4 py-3 rounded-xl hover:bg-white/10"
                      whileHover={{ x: 10, scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <motion.div
                        className="p-2 rounded-full bg-gradient-to-r from-emerald-500/20 to-transparent"
                        whileHover={{
                          scale: 1.2,
                          backgroundColor: "rgba(16, 185, 129, 0.3)",
                          rotate: 360
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <IconComponent className="w-5 h-5" />
                      </motion.div>

                      <div className="flex-1">
                        <div className="font-medium">{link.label}</div>
                        <div className="text-xs text-snow/60 mt-1">
                          {aiPersonality === 'quantum' ? link.aiHint : link.description}
                        </div>
                      </div>

                      <motion.div
                        className="w-2 h-2 bg-emerald-400 rounded-full opacity-0 group-hover:opacity-100"
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1, repeat: Infinity }}
                      />
                    </motion.a>
                  ) : (
                    <Link
                      key={link.href}
                      to={link.href}
                      className="group relative flex items-center space-x-3 text-base font-medium transition-all duration-300 hover:text-emerald-400 px-4 py-3 rounded-xl hover:bg-white/10"
                    >
                      <motion.div
                        className="p-2 rounded-full bg-gradient-to-r from-emerald-500/20 to-transparent"
                        whileHover={{
                          scale: 1.2,
                          backgroundColor: "rgba(16, 185, 129, 0.3)",
                          rotate: 360
                        }}
                        transition={{ duration: 0.3 }}
                      >
                        <IconComponent className="w-5 h-5" />
                      </motion.div>

                      <div className="flex-1">
                        <div className="font-medium">{link.label}</div>
                        <div className="text-xs text-snow/60 mt-1">
                          {aiPersonality === 'quantum' ? link.aiHint : link.description}
                        </div>
                      </div>
                    </Link>
                  );
                })}
              </nav>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Enhanced Main Content with Parallax */}
      <motion.div
        className="pt-20 relative"
        style={{ y: backgroundY }}
      >
        <Suspense fallback={<div className="h-screen flex items-center justify-center">
          <motion.div
            className="w-16 h-16 border-4 border-emerald-500/30 border-t-emerald-500 rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        </div>}>
          <Hero />
          <ValuesCarousel />
          <motion.div
            id="mission"
            whileInView={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 50 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
          >
            <Mission />
          </motion.div>
          <motion.div
            id="services"
            whileInView={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 50 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <Services />
          </motion.div>
          <motion.div
            whileInView={{ opacity: 1, scale: 1 }}
            initial={{ opacity: 0, scale: 0.95 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <InnovationLab />
          </motion.div>
          <motion.div
            id="footer"
            whileInView={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0, y: 30 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Footer />
          </motion.div>
        </Suspense>
      </motion.div>

      {/* Enhanced Auth Dialog */}
      <Dialog open={showAuth} onOpenChange={setShowAuth}>
        <DialogContent className="sm:max-w-[425px] bg-midnight/95 backdrop-blur-xl border border-emerald-500/20">
          <Auth />
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};

export default Index;
