@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --primary: 142 84% 50%;
    --primary-foreground: 144.9 80.4% 10%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142 84% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-midnight text-snow;
  }
}

html {
  scroll-behavior: smooth;
}

.glow-effect {
  box-shadow: 0 0 15px theme('colors.jungle');
}

.network-node {
  width: 4px;
  height: 4px;
  background: theme('colors.jungle');
  border-radius: 50%;
  position: absolute;
  animation: glow 2s infinite;
}

.network-line {
  height: 1px;
  background: linear-gradient(90deg, transparent, theme('colors.jungle'), transparent);
  position: absolute;
  transform-origin: left;
}